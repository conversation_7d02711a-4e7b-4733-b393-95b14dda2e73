package com.frt.usercore.api.test;

import com.frt.usercore.common.enums.exception.AuthErrorEnum;
import com.frt.usercore.common.utils.ValidateUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/test")
public class TestApi {

    @GetMapping("/hello")
    public String hello() {
        return "Hello, World!";
    }

    @GetMapping("/exception")
    public String exception() {
        throw AuthErrorEnum.SIGN_INVALID.exception();
    }

    @GetMapping("/validate-exception")
    public String validateException() {
       throw ValidateUtils.validateMsg("test");
//     ValidateUtils.validate(obj);
    }
}