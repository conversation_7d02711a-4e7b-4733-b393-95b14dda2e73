/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.generalgw.client.usercore.merchantadmin;

import com.frt.generalgw.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @version RoleManagerClient.java, v 0.1 2025-08-27 15:24 zhangling
 */
@FeignClient(value = "frt-usercore-dev", configuration = {FeignConfig.class})
public interface RoleManagerClient {
}