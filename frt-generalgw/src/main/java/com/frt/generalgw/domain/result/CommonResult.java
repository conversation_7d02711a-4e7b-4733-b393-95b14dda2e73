package com.frt.generalgw.domain.result;

import lombok.Data;

import java.io.Serializable;

/**
 * 通用操作结果
 *
 * <AUTHOR>
 * @version CommonResult.java, v 0.1 2025-08-27 14:52 zhangling
 */
@Data
public class CommonResult implements Serializable {

    private static final long serialVersionUID = -4423990568731270630L;

    /**
     * 响应码 0-成功 非0-失败
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private Object data;
}